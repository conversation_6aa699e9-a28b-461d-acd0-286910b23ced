# PRD – “Select Best Offer” Flow  

**Product:** <PERSON><PERSON><PERSON><PERSON> (Account Holder Web App)  
**Feature Name:** Select Best Offer  
**Owner:** Product / UX Team  
**Version:** v1.0  

---

## 1. Objective  

Enable the **Account Holder** to finalize an insurance auction by selecting one of the received offers (usually the best one), confirming they have signed with the chosen broker, uploading their new signed policy, and closing the process with full transparency and a positive user experience.  

---

## 2. Problem Statement  

Currently, the auction ends with multiple offers available, but users may lack guidance on what to do next. The platform must:  
- Guide the user to select a winning offer.  
- Ensure they confirm contract signing with the broker.  
- Require them to upload the signed policy document for record-keeping and wallet storage.  
- Provide a clear “completion” state showing savings, new insurer, agent, and next renewal date.  

---

## 3. User Flow (Step by Step)  

### Step 1 – Auction Closed (Screen 1 & 2)  
- User sees the **list of offers** (Best, Second, Third).  
- The “Best Offer” card includes a primary CTA: **Seleccionar mejor oferta**.  
- Other offers have a secondary CTA: **Seleccionar esta oferta**.  
- Once clicked, the flow begins.  

---

### Step 2 – Confirm Policy Signature (Screen 3)  
- A **modal dialog** appears:  
  - Title: **Confirm Policy Signature**.  
  - Displays **offer details** (agent, insurer, premium, savings).  
  - Confirmation question: *“Have you signed the policy with this agent?”*  
- Buttons:  
  - **Primary CTA (green):** “Yes, I have signed”.  
  - **Secondary CTA:** “Cancel”.  
- Business Rule: User must confirm signing before proceeding.  

---

### Step 3 – Upload New Policy (Screen 4)  
- Second modal appears after confirmation:  
  - Title: **Upload Your New Policy**.  
  - Dropzone component with upload instructions.  
  - Accepted file formats: **PDF, JPG, PNG**.  
  - Maximum file size: **10 MB**.  
  - Button: **Back**.  
- Business Rule: At least one valid file must be uploaded before proceeding.  

---

### Step 4 – Confirm and Finalize (Screen 5)  
- Third modal appears with a **review step**:  
  - Title: **Confirm and Finalize**.  
  - Shows selected agent, insurer, premium, savings.  
  - Displays uploaded file name.  
- Buttons:  
  - **Primary CTA (green):** “Confirm and Finalize”.  
  - **Secondary CTA:** “Back”.  
- Business Rule: User cannot complete process without a valid file upload.  

---

### Step 5 – Completion State (Screen 6)  
- Final view after confirmation:  
- The state of the auction is changed to SIGNED_POLICY.
  - Header cards show:  
    - Annual savings.  
    - New insurer.  
    - Assigned agent.  
    - Next renewal date.  
  - Banner: **Process Completed – Your new policy is confirmed and active**.  
  - Comparison section: Previous vs New policy (insurer, premium, coverages).  
  - Post-process actions:  
    - **Rate Experience**.  
    - **Contact Agent**.  
- Positive reinforcement: “You saved X € on your insurance.”  
- 
---

## 5. Technical Requirements  

- **Front-End:**  
  - Modal components with step-by-step navigation.  
  - File upload with drag-and-drop support, preview, and validation.  
  - State management for auction → confirmation → completion.  

- **Back-End:**  
  - API to register “Selected Offer” (offerId, userId, auctionId).  
  - API to upload and store new policy file (PDF/JPG/PNG).  
  - Status update: Auction → Signed_Policy → Completed.  
  - Link new policy file to user’s **Policy Wallet**.  

- **Validations:**  
  - File format & size validation on upload.  
  - Mandatory confirmation before advancing.  
  - Cannot finalize without a valid file.  

---

## 6. Edge Cases  

- User cancels at any step → system saves auction state as *“Offer Selected – Pending Completion”*.  
- File upload fails → error message + retry option.  
- Multiple uploads → allow replacement of previous file but keep only last confirmed version.  

---

## 7. UX / UI Notes  

- Use **progressive disclosure** (one modal per step → reduces cognitive load).  
- Primary CTAs always green and clearly visible.  
- Clear success messages at each step.  
- Strong visual feedback in completion state → reinforce savings and next renewal.  

---

## 8. Out of Scope  

- Automatic signing with insurers (handled offline with broker).  
- Multi-document upload (only one signed policy required at this stage).  