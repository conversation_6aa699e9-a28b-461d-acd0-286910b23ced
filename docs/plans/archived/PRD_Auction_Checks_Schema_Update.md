# Prisma Schema Update – Auction Signature & Policy Upload Checks  

**Product:** Zeeguros  
**Feature:** Select Best Offer – Signature & Upload Flow  
**Version:** v1.0  
**Owner:** Data/Backend Team  

---

## 1. Context  

In the new **“Select Best Offer”** flow, two critical confirmation steps are captured via checkboxes:  

1. **Modal 1 – Signature Confirmation**  
   - Checkbox: *“I confirm I have signed the contract with the broker/agent…”*  

2. **Modal 2 – Policy Upload Confirmation**  
   - Checkbox: *“I confirm the attached file is the signed policy…”*  

These confirmations must be persisted in the database to ensure auditability and correct state transitions.  

---

## 2. Where to Store the Data  

### Modal 1 – Signature Confirmation  
- Belongs to the **Auction**.  
- Reason: Signature confirmation is an action on the **auction result** (selected bid).  

**Fields to add in `Auction`:**
- `selectedAt` → When the offer was selected.  
- `signatureConfirmed` → Boolean.  
- `signatureConfirmedAt` → Timestamp.  
- `newPolicyDocumentId` → FK → `Documentation.id`.  
- `finalizedAt` → Timestamp when the wizard is fully completed.  

---

### Modal 2 – Policy Upload Confirmation  
- Belongs to the **Documentation** record of the uploaded file.  
- Reason: Attestation applies to the file itself.  

**Fields to add in `Documentation`:**
- `isPolicyAttested` → Boolean.  
- `policyAttestedAt` → Timestamp.  

**Enum update:**  
- Add `NEW_POLICY_DOCUMENT` to `DocumentType` for semantic clarity.  

---

## 3. Prisma Schema Changes  

### Auction Model  

```prisma
model Auction {
  // ...existing fields...

  // --- Selection & signature confirmation ---
  selectedAt           DateTime? @map("selected_at")
  signatureConfirmed   Boolean   @default(false) @map("signature_confirmed")
  signatureConfirmedAt DateTime? @map("signature_confirmed_at")

  // --- New policy document uploaded in step 2 ---
  newPolicyDocumentId String?         @unique @map("new_policy_document_id") @db.Uuid
  newPolicyDocument   Documentation?  @relation("AuctionNewPolicyDocument", fields: [newPolicyDocumentId], references: [id])

  // --- Wizard completed (step 3) ---
  finalizedAt DateTime? @map("finalized_at")

  @@index([selectedBidId])
  // ...existing @@map and @@schema...
}
```

---

### Documentation Model  

```prisma
model Documentation {
  // ...existing fields...

  // --- Attestation made in the upload modal ---
  isPolicyAttested Boolean   @default(false) @map("is_policy_attested")
  policyAttestedAt DateTime? @map("policy_attested_at")

  // Optional: inverse relation to Auction
  auctionNewPolicy Auction? @relation("AuctionNewPolicyDocument")

  // ...existing indexes, @@map and @@schema...
}
```

---

### DocumentType Enum  

```prisma
enum DocumentType {
  // ...existing values...
  POLICY_DOCUMENT
  NEW_POLICY_DOCUMENT  // <-- newly added
  // ...existing values...

  @@map("document_type")
  @@schema("public")
}
```

---

## 4. Migration Notes  

1. **Alter `auction` table**  
   - Add: `selected_at`, `signature_confirmed`, `signature_confirmed_at`,  
     `new_policy_document_id` (UUID, unique, FK), `finalized_at`.  

2. **Alter `documentation` table**  
   - Add: `is_policy_attested`, `policy_attested_at`.  

3. **Alter `document_type` enum**  
   - Add: `NEW_POLICY_DOCUMENT`.  

---

## 5. State & Business Rules  

- **On Modal 1 confirmation:**  
  - Set `auction.signatureConfirmed = true`.  
  - Set `auction.signatureConfirmedAt = now()`.  
  - Set `auction.selectedAt = now()`.  

- **On Modal 2 upload:**  
  - Create `Documentation` record with:  
    - `type = NEW_POLICY_DOCUMENT`  
    - `relatedAuctionId = auction.id`  
    - `isPolicyAttested = true`  
    - `policyAttestedAt = now()`  
  - Link it via `auction.newPolicyDocumentId`.  

- **On final confirmation:**  
  - Set `auction.status = SIGNED_POLICY`.  
  - Set `auction.finalizedAt = now()`.  

---

## 6. Edge Cases  

- If user cancels:  
  - Keep `auction.signatureConfirmed = false`.  
  - Keep `auction.newPolicyDocumentId = null`.  
- If file upload fails:  
  - Do not set `isPolicyAttested`.  
- If user re-uploads:  
  - Allow replacement of the document, only last one is linked.  

---

## 7. Out of Scope  

- Automatic insurer signature.  
- Multi-document upload (only one signed policy required).  
- Legal metadata (e.g., IP address, device ID).  

---
