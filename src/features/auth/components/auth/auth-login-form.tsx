"use client";

import { useState } from "react";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Eye, EyeOff, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { login } from "@/features/auth/actions/login";
// Logo is now in public directory

export function AuthLoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get the returnUrl from the URL query parameters
  const returnUrl = searchParams.get("returnUrl") || "/policies";

  return (
    <div
      className={cn("flex flex-col gap-4 p-6 max-w-md mx-auto", className)}
      {...props}
    >
      <form
        action={async (formData: FormData) => {
          setIsLoading(true);
          setError(null);

          try {
            formData.append('returnUrl', returnUrl);
            const result = await login(formData);

            // Only set error if we got an error result
            // If login succeeds, we'll be redirected and this code won't execute
            if (result?.error) {
              setError(result.error);
              setIsLoading(false);
            }
          } catch (error) {
            // Ignore Next.js redirect errors which are expected during successful login
            if (!(error instanceof Error && (
              error.message.includes('NEXT_REDIRECT') ||
              error.message.includes('navigation')
            ))) {
              console.error("Login error:", error);
              setError("Ha ocurrido un error inesperado. Por favor, actualiza la página e inténtalo de nuevo.");
              setIsLoading(false);
            }
          }
        }}
        className="flex flex-col gap-4"
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col items-center gap-2 mb-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-md">
              <Image
                src="/logo-short-dark.svg"
                alt="Zeeguros Logo"
                className="h-8 w-8"
                width={56}
                height={54}
              />
            </div>
            <h1 className="text-xl font-bold">
              Iniciar Sesión
            </h1>
          </div>

          {error && (
            <div className="bg-destructive/10 text-destructive p-4 rounded-md flex items-start gap-3 border border-destructive/20">
              <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium mb-1">{error.includes('No se pudo conectar con el servidor') ? 'Problema de conexión' : 'Error de inicio de sesión'}</p>
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="email">Correo electrónico</Label>
            <Input
              id="email"
              name="email"
              placeholder="<EMAIL>"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
              required
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="password">Contraseña</Label>
              <Link
                href="/forgot-password"
                className="text-sm text-primary hover:underline hover:text-primary"
              >
                ¿Olvidaste tu contraseña?
              </Link>
            </div>
            <div className="relative">
              <Input
                id="password"
                name="password"
                placeholder="••••••••"
                type={showPassword ? "text" : "password"}
                autoCapitalize="none"
                autoComplete="current-password"
                disabled={isLoading}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-2.5 text-primary"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>
        </div>

        <Button
          type="submit"
          variant="default"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? "Iniciando sesión..." : "Iniciar sesión"}
        </Button>
      </form>

      <div className="text-center text-sm">
        ¿No tienes una cuenta?{" "}
        <Link href="/signup" className="text-primary hover:underline hover:text-primary">
          Regístrate
        </Link>
      </div>
    </div>
  );
}
