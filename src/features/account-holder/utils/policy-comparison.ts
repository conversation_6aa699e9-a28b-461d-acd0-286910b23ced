import { PolicyData } from '@/types/policy';
import { formatCurrency } from '@/lib/utils';
import {
  compareCoverages,
  getKeyCoverageDifferences,
  isBidCoverageDataAvailable,
  CoverageComparisonResult
} from './coverage-normalization';
import { Coverage, GuaranteeType, Prisma } from '@prisma/client';

// Types for comparison data
export interface BidComparisonData {
  id: string;
  annualPremium: number;
  brokerName: string;
  brokerCompany: string;
  createdAt: string;
  hasDocument: boolean;
  bidCoverages?: any[];
}

export interface ComparisonResult {
  field: string;
  label: string;
  policyValue: string;
  bidValue: string;
  difference?: {
    type: 'better' | 'worse' | 'neutral';
    amount?: number;
    percentage?: number;
  };
  hasData: {
    policy: boolean;
    bid: boolean;
  };
}

export interface PolicyBidComparison {
  summary: {
    premiumComparison: ComparisonResult;
    insurerComparison: ComparisonResult;
    brokerComparison: ComparisonResult;
  };
  details: ComparisonResult[];
  coverageComparison: {
    available: boolean;
    message: string;
    requiresDocumentAnalysis: boolean;
    coverages?: CoverageComparisonResult[];
    keyCoverageDifferences?: CoverageComparisonResult[];
  };
}

/**
 * Compare a policy with a bid offer
 */
export function comparePolicyWithBid(
  policy: PolicyData,
  bid: BidComparisonData
): PolicyBidComparison {
  // Parse current premium from policy (handle Spanish currency formatting)
  // Spanish format: "610,00 €" -> convert comma to dot for parsing
  const currentPremium = parseFloat(
    policy.annualPremium
      .replace(/[^\d,.-]/g, '') // Remove currency symbols and spaces
      .replace(',', '.') // Convert Spanish decimal comma to dot
  );
  const bidPremium = bid.annualPremium;
  
  // Calculate premium difference
  const premiumDifference = bidPremium - currentPremium;
  const premiumPercentage = currentPremium > 0 ? (premiumDifference / currentPremium) * 100 : 0;
  
  // Determine if bid is better (lower premium is better)
  const premiumComparisonType: 'better' | 'worse' | 'neutral' = 
    premiumDifference < 0 ? 'better' : 
    premiumDifference > 0 ? 'worse' : 'neutral';

  // For savings calculation: if bid is better (lower), amount should be currentPremium - bidPremium (positive savings)
  // If bid is worse (higher), amount should be bidPremium - currentPremium (positive additional cost)
  const savingsAmount = premiumComparisonType === 'better' ? currentPremium - bidPremium : Math.abs(premiumDifference);

  const summary = {
    premiumComparison: {
      field: 'annualPremium',
      label: 'Prima Anual',
      policyValue: policy.annualPremium,
      bidValue: formatCurrency(bidPremium),
      difference: {
        type: premiumComparisonType,
        amount: savingsAmount,
        percentage: Math.abs(premiumPercentage)
      },
      hasData: { policy: true, bid: true }
    } as ComparisonResult,
    
    insurerComparison: {
      field: 'insurer',
      label: 'Aseguradora',
      policyValue: policy.insurer,
      bidValue: bid.brokerCompany,
      difference: {
        type: 'neutral' as const
      },
      hasData: { policy: !!policy.insurer, bid: !!bid.brokerCompany }
    } as ComparisonResult,
    
    brokerComparison: {
      field: 'broker',
      label: 'Agente/Broker',
      policyValue: 'Actual', // Current policy doesn't have broker info
      bidValue: bid.brokerName,
      difference: {
        type: 'neutral' as const
      },
      hasData: { policy: false, bid: !!bid.brokerName }
    } as ComparisonResult
  };

  // Detailed comparisons for other fields
  const details: ComparisonResult[] = [
    {
      field: 'product',
      label: 'Producto',
      policyValue: policy.product,
      bidValue: 'No disponible', // Bid doesn't include product details
      hasData: { policy: !!policy.product, bid: false }
    },
    {
      field: 'policyType',
      label: 'Tipo de Póliza',
      policyValue: policy.policyType,
      bidValue: 'No disponible',
      hasData: { policy: !!policy.policyType, bid: false }
    },
    {
      field: 'validity',
      label: 'Vigencia',
      policyValue: policy.validity,
      bidValue: 'No disponible',
      hasData: { policy: !!policy.validity, bid: false }
    }
  ];

  // Helper function to safely convert decimal values to Prisma Decimal
  const safeNumberToDecimal = (value: number | null | undefined): Prisma.Decimal | null => {
    if (value === null || value === undefined) return null;
    return new Prisma.Decimal(value);
  };

  // Convert PolicyCoverage[] to Coverage[] format for comparison
  const convertPolicyCoverages = (policyCoverages: PolicyData['coverages']): Coverage[] => {
    return policyCoverages.map(coverage => ({
      id: '', // Not needed for comparison
      policyId: '', // Not needed for comparison
      type: coverage.guaranteeType,
      customName: coverage.customName || null,
      description: coverage.description,
      limit: safeNumberToDecimal(coverage.limit),
      limitIsUnlimited: coverage.limitIsUnlimited || false,
      limitIsFullCost: coverage.limitIsFullCost || false,
      limitPerDay: safeNumberToDecimal(coverage.limitPerDay),
      limitMaxDays: coverage.limitMaxDays || null,
      limitMaxMonths: coverage.limitMaxMonths || null,
      liabilityBodilyCap: safeNumberToDecimal(coverage.liabilityBodilyCap),
      liabilityPropertyCap: safeNumberToDecimal(coverage.liabilityPropertyCap),
      deductible: safeNumberToDecimal(coverage.deductible),
      deductiblePercent: safeNumberToDecimal(coverage.deductiblePercent)
    }));
  };

  // Coverage comparison with normalization
  const hasBidCoverageData = isBidCoverageDataAvailable(bid.bidCoverages || []);
  const normalizedPolicyCoverages = policy.coverages ? convertPolicyCoverages(policy.coverages) : [];
  const coverageComparisons = normalizedPolicyCoverages.length > 0 ? compareCoverages(normalizedPolicyCoverages, bid.bidCoverages || []) : [];
  
  const coverageComparison = {
    available: policy.coverages && policy.coverages.length > 0,
    message: !policy.coverages || policy.coverages.length === 0
      ? 'No hay información de coberturas disponible en la póliza actual'
      : !hasBidCoverageData && bid.hasDocument
      ? 'Las coberturas detalladas requieren análisis del documento adjunto'
      : !hasBidCoverageData
      ? 'No hay documento de cobertura disponible para esta oferta'
      : 'Comparación de coberturas disponible',
    requiresDocumentAnalysis: bid.hasDocument && !hasBidCoverageData,
    coverages: coverageComparisons.length > 0 ? coverageComparisons : undefined,
    keyCoverageDifferences: coverageComparisons.length > 0 ? coverageComparisons : undefined
  };

  return {
    summary,
    details,
    coverageComparison
  };
}

/**
 * Format comparison difference for display
 */
export function formatComparisonDifference(
  difference: ComparisonResult['difference']
): string {
  if (!difference) return '';
  
  const { type, amount, percentage } = difference;
  
  if (type === 'neutral') return '';
  
  const sign = type === 'better' ? '-' : '+';
  const amountStr = amount ? formatCurrency(amount) : '';
  const percentageStr = percentage ? `${percentage.toFixed(1)}%` : '';
  
  if (amountStr && percentageStr) {
    return `${sign}${amountStr} (${sign}${percentageStr})`;
  } else if (amountStr) {
    return `${sign}${amountStr}`;
  } else if (percentageStr) {
    return `${sign}${percentageStr}`;
  }
  
  return '';
}

/**
 * Get comparison indicator icon/color based on difference type
 */
export function getComparisonIndicator(type: 'better' | 'worse' | 'neutral') {
  switch (type) {
    case 'better':
      return {
        icon: '↓',
        color: 'text-brand-aquamarine-green',
        bgColor: 'bg-brand-aquamarine-green/10',
        label: 'Mejor'
      };
    case 'worse':
      return {
        icon: '↑',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        label: 'Peor'
      };
    case 'neutral':
    default:
      return {
        icon: '=',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        label: 'Igual'
      };
  }
}

/**
 * Validate if comparison data is sufficient
 */
export function validateComparisonData(
  policy: PolicyData | undefined,
  bid: BidComparisonData | undefined
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!policy) {
    errors.push('Datos de póliza no disponibles');
  }
  
  if (!bid) {
    errors.push('Datos de oferta no disponibles');
  }
  
  if (policy && !policy.annualPremium) {
    errors.push('Prima anual de la póliza no disponible');
  }
  
  if (bid && !bid.annualPremium) {
    errors.push('Prima anual de la oferta no disponible');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}