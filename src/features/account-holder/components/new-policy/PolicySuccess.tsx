"use client";

import { <PERSON><PERSON><PERSON><PERSON>, Eye, FileText, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useSidebar } from "@/components/ui/sidebar";

interface PolicySuccessProps {
  policyNumber?: string;
}

export function PolicySuccess({ policyNumber }: PolicySuccessProps) {
  const { state } = useSidebar();
  
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* What's Next Section */}
      <div className="bg-muted/95 p-6 rounded-lg space-y-4">
        <h2 className="font-semibold text-center text-lg">¿Qué sucede después?</h2>
        
        <div className="grid gap-4">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <p className="font-medium">Revisión y validación</p>
              <p className="text-sm text-muted-foreground">
                Estamos verificando que toda la información esté completa y sea precisa. Te avisaremos al correo cuando terminemos la revisión.
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <p className="font-medium">Inicio de la subasta</p>
              <p className="text-sm text-muted-foreground">
                Una vez validada tu póliza, lanzaremos la subasta para encontrar las mejores ofertas en menos de 48 horas.
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            <div>
              <p className="font-medium">Notificación de resultados</p>
              <p className="text-sm text-muted-foreground">
                Te avisaremos tan pronto tengamos las mejores propuesta para ti.
              </p>
            </div>
          </div>
        </div>

        <div className="text-center pt-2">
          <p className="font-medium text-primary">
            🔔 No necesitas hacer nada más por ahora.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            ¡Nos aseguraremos de que encuentres la mejor oferta posible!
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div
        className={`fixed bottom-0 left-0 right-0 z-10 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200 ${
          state === "collapsed"
            ? "md:left-[calc(var(--sidebar-width-icon)_+_1rem)]"
            : "md:left-[var(--sidebar-width)]"
        }`}
      >
        <div className="container flex h-16 items-center justify-between">
          <Button asChild variant="outline" className="hover:bg-primary hover:border-primary hover:text-primary-foreground">
            <a href="/account-holder/policies">
              <FileText className="mr-2 h-4 w-4" />
              Ir a Mis Pólizas
            </a>
          </Button>

          <Button asChild className="bg-primary hover:bg-primary hover:border-primary text-primary-foreground">
            <a href="/account-holder/policies">
              Ver mi subasta
              <ArrowRight className="ml-2 h-4 w-4" />
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}