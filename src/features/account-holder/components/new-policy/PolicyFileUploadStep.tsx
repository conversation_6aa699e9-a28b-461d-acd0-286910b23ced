"use client";

import { useState, useCallback, useRef, useMemo } from "react";
import Link from "next/link";
import { FileUpload } from "@/components/ui/file-upload";
import { useToast } from "@/components/ui/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";

interface PolicyFileUploadStepProps {
  onBack: () => void;
  onContinue: () => void;
  onFileSelect?: (file: File) => void;
  onTermsAcceptanceChange?: (accepted: boolean) => void;
  isSubmitting?: boolean;
  // Customization props for different use cases
  title?: string;
  description?: string;
  acceptedFileTypes?: string[];
  maxFileSize?: number;
  showTermsCheckbox?: boolean;
  termsText?: string;
  continueButtonText?: string;
  backButtonText?: string;
  processingText?: string;
  // For auction use case
  isAuctionMode?: boolean;
  selectedBid?: {
    brokerName: string;
    brokerCompany: string;
  } | null;
  // Show important section after upload area
  showImportantSection?: boolean;
}

export function PolicyFileUploadStep({
  onBack,
  onContinue,
  onFileSelect,
  onTermsAcceptanceChange,
  isSubmitting = false,
  title = "Arrastra tu PDF o imagen, o haz clic para seleccionar un archivo",
  description = "Aceptamos PDF, JPG, PNG. Máx 10 MB",
  acceptedFileTypes = ["application/pdf", "image/jpeg", "image/png"],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  showTermsCheckbox = true,
  termsText,
  continueButtonText = "Continuar",
  backButtonText = "Atrás",
  processingText = "Subiendo tu póliza...",
  isAuctionMode = false,
  selectedBid = null,
  showImportantSection = false,
}: PolicyFileUploadStepProps) {
  const { toast } = useToast();

  const [, setSelectedFile] = useState<File | null>(null);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractionError, setExtractionError] = useState<string | null>(null);
  const [isFileValid, setIsFileValid] = useState(false);

  const extractionToastShownRef = useRef(false);

  const handleFileSelect = useCallback(
    async (file: File) => {
      setSelectedFile(file);
      setExtractionError(null);
      setIsProcessing(true);

      try {
        // Call the parent component's onFileSelect if provided
        onFileSelect?.(file);

        // For auction mode, AI validation is handled by the API route
        // For new policy mode, AI validation is handled by the service
        // Here we just simulate the UI flow for file selection

        setIsFileValid(true);
        extractionToastShownRef.current = true;
        setIsProcessing(false);
      } catch (error: any) {
        console.error("Error during file processing:", error);
        const errorMessage =
          error.message || "No se pudo procesar el documento.";
        setExtractionError(errorMessage);
        toast({
          variant: "destructive",
          title: "Error en la extracción",
          description:
            "Ocurrió un error inesperado. Por favor, inténtelo de nuevo.",
        });
        setIsProcessing(false);
      }
    },
    [onFileSelect, toast]
  );

  const handleFileRemove = useCallback(() => {
    setSelectedFile(null);
    setIsFileValid(false);
    setIsProcessing(false);
    setExtractionError(null);
    extractionToastShownRef.current = false;
  }, []);

  const handleContinue = useCallback(() => {
    console.log('Button clicked!', { canContinue, isFileValid, acceptTerms, isProcessing });
    const shouldCheckTerms = showTermsCheckbox ? acceptTerms : true;
    if (!isFileValid || !shouldCheckTerms || isProcessing || isSubmitting) return;
    onContinue();
  }, [isFileValid, acceptTerms, isProcessing, isSubmitting, onContinue, showTermsCheckbox]);

  const canContinue = useMemo(() => {
    const shouldCheckTerms = showTermsCheckbox ? acceptTerms : true;
    return isFileValid && shouldCheckTerms && !isProcessing && !isSubmitting;
  }, [isFileValid, acceptTerms, isProcessing, isSubmitting, showTermsCheckbox]);

  // Generate default terms text based on mode
  const defaultTermsText = useMemo(() => {
    if (isAuctionMode && selectedBid) {
      return (
        <>
          Confirmo que el documento adjunto es la póliza firmada con{" "}
          <strong>{selectedBid.brokerName}</strong> de la aseguradora{" "}
          <strong>{selectedBid.brokerCompany}</strong> y que toda la información es correcta y completa.
        </>
      );
    }
    return "He leído y acepto los términos y condiciones de Zeeguros y que me contactéis con mis ofertas personalizadas.";
  }, [isAuctionMode, selectedBid]);

  const finalTermsText = termsText || defaultTermsText;

  return (
    <>
      <div className="flex flex-col space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          onFileRemove={handleFileRemove}
          isProcessing={isProcessing}
          processingText={processingText}
          error={extractionError}
          onErrorClear={() => setExtractionError(null)}
          title={title}
          description={description}
          acceptedFileTypes={acceptedFileTypes}
          maxFileSize={maxFileSize}
        />

        {/* Important Information - Show after upload area for auction mode */}
        {showImportantSection && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium text-amber-800">Importante</span>
            </div>
            <ul className="space-y-2 text-sm text-amber-800">
              <li>• Una vez subida la póliza, el proceso será irreversible</li>
              <li>• Asegúrese de que el documento esté completo y firmado</li>
              <li>• Encontrarás en tu wallet de pólizas el documento adjuntado, una vez se haya terminado la verificación.</li>
            </ul>
          </div>
        )}

        {showTermsCheckbox && (
          <div className="flex items-start space-x-3 mt-4">
            <Checkbox
              id="acceptTerms"
              name="acceptTerms"
              checked={acceptTerms}
              onCheckedChange={(checked) => {
                const isAccepted = checked as boolean;
                setAcceptTerms(isAccepted);
                onTermsAcceptanceChange?.(isAccepted);
              }}
              required
              className="mt-1"
            />
            <label htmlFor="acceptTerms" className="text-sm leading-relaxed text-gray-700">
              {isAuctionMode ? (
                <>
                  {finalTermsText}
                  <span className="text-red-500">*</span>
                </>
              ) : (
                <>
                  He leído y acepto los{" "}
                  <Link
                    href="https://zeeguros.com/terminos-condiciones/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline underline-offset-4 hover:text-primary"
                  >
                    términos y condiciones
                  </Link>{" "}
                  de Zeeguros y que me contactéis con mis ofertas personalizadas.
                  <span className="text-red-500">*</span>
                </>
              )}
            </label>
          </div>
        )}
      </div>

      {/* Fixed Footer Navigation - Adjusted for modal */}
      <div className="flex items-center justify-between pt-6 gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          className="flex-1 h-12 hover:bg-primary hover:border-primary hover:text-primary-foreground"
        >
          {backButtonText}
        </Button>

        <Button
          type="button"
          className={
            !canContinue
              ? "flex-1 h-12 cursor-not-allowed bg-gray-300 hover:bg-gray-300 border-gray-400"
              : "flex-1 h-12 btn-brand-primary"
          }
          disabled={!canContinue || isProcessing || isSubmitting}
          onClick={handleContinue}
        >
          {isSubmitting ? "Procesando..." : continueButtonText}
        </Button>
      </div>
    </>
  );
}
