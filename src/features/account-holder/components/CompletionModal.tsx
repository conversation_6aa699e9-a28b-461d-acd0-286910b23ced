"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { formatCurrency, formatDate } from "@/lib/utils";
import { CheckCircle, Calendar, PiggyBank, Shield, User, FileText } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { AuctionBid } from "../types/auction";

interface CompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedBid: AuctionBid | null;
  currentPremium: number;
  nextRenewalDate?: string;
  auctionId: string;
  onComplete?: () => void;
}

export function CompletionModal({
  isOpen,
  onClose,
  selectedBid,
  currentPremium,
  nextRenewalDate,
  auctionId,
  onComplete,
}: CompletionModalProps) {
  const [policyConfirmed, setPolicyConfirmed] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  if (!selectedBid) return null;

  const savings = currentPremium - selectedBid.annualPremium;
  const savingsPercentage = ((savings / currentPremium) * 100).toFixed(1);
  const renewalDate = nextRenewalDate ? new Date(nextRenewalDate) : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000);

  const handleComplete = async () => {
    if (!policyConfirmed || !termsAccepted) {
      toast({
        title: "Confirmación requerida",
        description: "Debe confirmar ambas casillas para completar el proceso",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch(`/api/account-holder/auctions/${auctionId}/complete`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Error al completar la subasta');
      }

      toast({
        title: "¡Subasta completada!",
        description: "Tu nueva póliza está ahora activa.",
      });
      
      onComplete?.();
      onClose();
      
      // Redirect to policies page
      window.location.href = '/account-holder/policies';
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al completar la subasta",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    setPolicyConfirmed(false);
    setTermsAccepted(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-brand-aquamarine-green" />
            ¡Proceso Completado Exitosamente!
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Success Message */}
          <Card className="border-brand-aquamarine-green/30 bg-brand-aquamarine-green/10">
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <CheckCircle className="h-12 w-12 text-brand-aquamarine-green mx-auto" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Su nueva póliza ha sido procesada
                </h3>
                <p className="text-gray-700">
                  El proceso de selección de mejor oferta se ha completado correctamente
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Savings Summary */}
          {savings > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PiggyBank className="h-5 w-5 text-brand-aquamarine-green" />
                  Resumen de Ahorros
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <p className="text-sm text-muted-foreground">Prima Anterior</p>
                    <p className="text-xl font-bold text-red-600">
                      {formatCurrency(currentPremium)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-brand-aquamarine-green/10 rounded-lg">
                    <p className="text-sm text-muted-foreground">Nueva Prima</p>
                    <p className="text-xl font-bold text-brand-aquamarine-green">
                      {formatCurrency(selectedBid.annualPremium)}
                    </p>
                  </div>
                </div>
                
                <div className="text-center p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                  <p className="text-sm text-muted-foreground">Ahorro Total</p>
                  <p className="text-3xl font-bold text-blue-600">
                    {formatCurrency(savings)}
                  </p>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 mt-2">
                    {savingsPercentage}% de descuento
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* New Insurer Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600" />
                Nueva Aseguradora
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <User className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="font-medium">{selectedBid.brokerName}</p>
                    <p className="text-sm text-muted-foreground">
                      Agente: {selectedBid.brokerCompany}
                    </p>
                  </div>
                </div>
                <Badge variant="outline" className="border-brand-aquamarine-green text-brand-aquamarine-green">
                  Activa
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div>
                  <p className="text-sm text-muted-foreground">Prima Anual</p>
                  <p className="font-semibold">{formatCurrency(selectedBid.annualPremium)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Próxima Renovación</p>
                  <p className="font-semibold">{formatDate(renewalDate)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Próximos Pasos
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-4 w-4 text-brand-aquamarine-green mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Póliza Activada</p>
                  <p className="text-sm text-muted-foreground">
                    Su nueva póliza está ahora activa y aparecerá en su cartera
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <FileText className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Documentación</p>
                  <p className="text-sm text-muted-foreground">
                    Recibirá una copia de la póliza firmada por correo electrónico
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Calendar className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Recordatorio de Renovación</p>
                  <p className="text-sm text-muted-foreground">
                    Le notificaremos 30 días antes de la fecha de renovación
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Final Confirmation */}
          <Card className="border-amber-200 bg-amber-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-amber-800">
                <CheckCircle className="h-5 w-5" />
                Confirmación Final
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="policy-confirmed"
                  checked={policyConfirmed}
                  onCheckedChange={(checked) => setPolicyConfirmed(checked as boolean)}
                  className="mt-1"
                />
                <Label htmlFor="policy-confirmed" className="text-sm leading-relaxed text-amber-800">
                  Confirmo que he recibido y revisado mi nueva póliza de seguro con <strong>{selectedBid.brokerName}</strong> de <strong>{selectedBid.brokerCompany}</strong> y que toda la información es correcta.
                </Label>
              </div>
              
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="terms-accepted"
                  checked={termsAccepted}
                  onCheckedChange={(checked) => setTermsAccepted(checked as boolean)}
                  className="mt-1"
                />
                <Label htmlFor="terms-accepted" className="text-sm leading-relaxed text-amber-800">
                  Acepto los términos y condiciones del servicio y confirmo que deseo activar mi nueva póliza de seguro.
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isProcessing}
              className="flex-1 border-gray-300 text-black hover:bg-gray-50 hover:text-black"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleComplete}
              disabled={!policyConfirmed || !termsAccepted || isProcessing}
              className="flex-1 bg-brand-aquamarine-green hover:bg-brand-aquamarine-green/90 text-black font-medium"
            >
              {isProcessing ? "Finalizando..." : "Finalizar y Confirmar"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}