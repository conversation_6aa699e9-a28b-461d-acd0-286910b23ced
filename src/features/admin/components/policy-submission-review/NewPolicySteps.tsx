import { useEffect, useState } from "react";
import { FileUpload } from "@/components/ui/file-upload";
import { zodResolver } from "@hookform/resolvers/zod";
// import { usePolicySubmissionReview } from "@/features/admin/hooks/usePolicySubmissionReview";
import { PolicySuccess } from "../PolicySuccess";
import { policySchema } from "./PolicyDataForm";
import { PolicyStepper } from "../PolicyStepper";
import { PolicyReview } from "./PolicyReview";
import { useToast } from "@/components/ui/use-toast";
import { createClient } from "@/lib/supabase/client";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  CardDescription,
  CardContent,
  CardHeader,
  CardTitle,
  Card,
} from "@/components/ui/card";

import {
  PaymentPeriod,
  GuaranteeType,
  AssetsType,
  PolicyType,
  GarageType,
  PartyRole,
  UsageType,
  FuelType,
  KmRange,
} from "@prisma/client";

export type Coverage = {
  id?: string;
  type: GuaranteeType;
  customName?: string;
  limit?: number;
  deductible?: number;
  description?: string;
};

export type PolicyData = {
  // Policy details
  policyNumber: string;
  insurerName: string;
  policyType: PolicyType | null;
  productName: string | null;
  startDate: Date | null;
  endDate: Date | null;
  paymentPeriod: PaymentPeriod | null;
  premium: number | null;

  // Insured details
  insuredParties: {
    personId?: string;
    fullName: string;
    dni: string;
    roles: PartyRole[];
  }[];

  // Asset details
  brand: string;
  model: string;
  year: number | null;
  version: string | null;
  chassisNumber: string;
  licensePlate: string;
  firstRegistrationDate: Date | null;
  type: AssetsType | null;
  fuelType: FuelType | null;
  usageType: UsageType | null;
  kmPerYear: KmRange | null;
  garageType: GarageType | null;
  seats: number | null;
  powerCv: number | null;
  isLeased: boolean;

  // Coverage details
  coverages: Coverage[];
};

const initializeNewPolicy = (): PolicyData => {
  const startDate = new Date();
  const endDate = new Date();
  endDate.setFullYear(startDate.getFullYear() + 1);
  return {
    policyNumber: "",
    insurerName: "",
    policyType: null,
    productName: "",
    startDate: null,
    endDate: null,
    paymentPeriod: null,
    premium: null,
    insuredParties: [],
    brand: "",
    model: "",
    year: null,
    version: "",
    chassisNumber: "",
    licensePlate: "",
    firstRegistrationDate: null,
    type: null,
    fuelType: null,
    usageType: null,
    kmPerYear: null,
    garageType: null,
    seats: null,
    powerCv: null,
    isLeased: false,
    coverages: [],
  };
};

export function NewPolicySteps() {
  const [user, setUser] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<any>(undefined);
  const [policyData, setPolicyData] = useState<PolicyData>(
    initializeNewPolicy()
  );
  const supabase = createClient();
  const { toast } = useToast();

  useEffect(() => {
    async function getUser() {
      try {
        const { data } = await supabase.auth.getUser();
        setUser(data.user);
      } catch (error) {
        console.error("Error getting user:", error);
      }
    }
    getUser();
  }, [supabase.auth]);

  const handleContinue = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const handleBack = () => {
    setCurrentStep((prev) => Math.max(0, prev - 1));
  };

  const handleSubmit = async () => {
    if (!policyData) {
      toast({
        variant: "destructive",
        title: "Falta información",
        description: "Por favor completa todos los campos requeridos.",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      if (!user) {
        toast({
          variant: "destructive",
          title: "Sesión no iniciada",
          description: "Debes iniciar sesión para guardar la póliza.",
        });
        return;
      }

      if (uploadedFile) {
        const userId = user.id;
        const sanitizeFilename = (filename: string) => {
          const lastDotIndex = filename.lastIndexOf(".");
          const name =
            lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
          const extension =
            lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
          const sanitizedName = name
            .replace(/[^a-zA-Z0-9\-_]/g, "_")
            .replace(/_{2,}/g, "_")
            .replace(/^_+|_+$/g, "");
          return sanitizedName + extension;
        };
        const sanitizedFilename = sanitizeFilename(uploadedFile.name);
        const filePath = `${userId}/${Date.now()}_${sanitizedFilename}`;
        const { error: uploadError } = await supabase.storage
          .from("policy_documents")
          .upload(filePath, uploadedFile);

        if (uploadError) {
          throw new Error(`Error subiendo documento: ${uploadError.message}`);
        }
        console.log("Document uploaded to:", filePath);
      }

      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: "Póliza registrada con éxito",
        description: "Tu póliza ha sido guardada y la subasta ha sido creada.",
      });

      setCurrentStep(4);
    } catch (error: any) {
      console.error("Error en registro de póliza:", error);
      toast({
        variant: "destructive",
        title: "Error al guardar póliza",
        description:
          error.message ||
          "Hubo un error al guardar tu póliza. Por favor intenta de nuevo.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const form = useForm<z.infer<typeof policySchema>>({
    resolver: zodResolver(policySchema),
    defaultValues: policyData ?? undefined,
  });

  useEffect(() => {
    if (policyData) {
      form.reset(policyData);
    }
  }, [policyData, form]);

  const steps = [
    {
      title: "Tipo de Póliza",
      description: "Selecciona tu tipo de poliza",
    },
    {
      title: "Cargar Póliza",
      description: "Carga tu póliza de seguro",
    },
    {
      title: "Poliza Almacenada",
      description: "Verifica que toda la información sea correcta",
    },
  ];

  return (
    <div className="space-y-6">
      <CardHeader className="space-y-6">
        <CardTitle>Haz que tu seguro compita por ti 🏁</CardTitle>
        
        <CardDescription className="space-y-2">
          <b>
            Recibe hasta 3 ofertas firmes en menos de 48h y ahorra hasta un 40
          </b>
          <p>
            % ¿Tienes una póliza de seguro vigente? Sube tu póliza actual y deja
            que las aseguradoras pujen. Tú eliges la mejor. Gratis y sin
            compromiso 😎
          </p>
        </CardDescription>
        <CardContent className="space-y-6">
          <PolicyStepper currentStep={currentStep} steps={steps} />
          {currentStep === 0 && (
            <div className="mt-4 space-y-6 text-center">
              <h3>¿Qué tipo de póliza quieres renovar?</h3>
              <div className="flex justify-center gap-6">
                <Card
                  className="cursor-pointer hover:bg-gray-100 w-1/3 border-gray-400 text-center"
                  onClick={() => {
                    handleContinue();
                  }}
                >
                  <CardContent className="p-4 space-y-4">
                    <h2 className="text-5xl font-medium text-gray-500">🚗</h2>
                    <h3 className="text-xs text-gray-500">
                      Pólizas de Automóvil
                    </h3>
                  </CardContent>
                </Card>
                <Card
                  className="cursor-pointer hover:bg-primary/10 w-1/3 border-primary text-center"
                  onClick={() => {
                    handleContinue();
                  }}
                >
                  <CardContent className="p-4 space-y-4">
                    <h4 className="text-5xl font-medium text-gray-500">🏍️</h4>
                    <h3 className="text-xs text-gray-500">
                      Pólizas de Motocicleta
                    </h3>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
          {currentStep === 1 && policyData && (
            <div className="space-y-6 pb-24">
              {/* <PolicyDataForm
                form={form}
                onDataUpdate={handleDataUpdate}
                onBack={handleBack}
                onContinue={() => {
                  form.trigger().then((isValid) => {
                    if (isValid) {
                      // handleDataUpdate(form.getValues());
                      handleContinue();
                    }
                  });
                }}
              /> */}
              <FileUpload
                onExtractionComplete={(data) => {
                  handleContinue();
                }}
                onBack={handleBack}
                onContinue={() => {
                  form.trigger().then((isValid) => {
                    if (isValid) {
                      handleContinue();
                    }
                  });
                  handleContinue();
                }}
                onFillManually={handleContinue}
              />
            </div>
          )}
          {currentStep === 2 && policyData && (
            <div className="mt-8">
              <PolicyReview />
            </div>
          )}
        </CardContent>
      </CardHeader>


    </div>
  );
}
