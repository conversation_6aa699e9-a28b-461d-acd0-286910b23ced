import { db } from "@/lib/db";
import { uploadToR2 } from "@/lib/r2";
import { validatePolicyDocument } from "@/lib/file-validation";
import { sanitizeFilename } from "@/lib/utils";
import { DocumentationType } from "@prisma/client";
import { GoogleGenerativeAI, Part } from "@google/generative-ai";

// Initialize the Gemini API client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

export interface PolicyUploadOptions {
  file: File;
  fileName?: string;
  userId: string; // User ID for folder organization (instead of accountHolderId)
  accountHolderId: string; // Still needed for database record
  documentType: DocumentationType;
  relatedAuctionId?: string;
  isPolicyAttested?: boolean;
  location?: string;
  skipAIValidation?: boolean; // For cases where AI validation is not needed (default: true for <PERSON>)
}

export interface PolicyUploadResult {
  documentation: {
    id: string;
    fileName: string | null;
    url: string; // R2 key for secure access via /api/documents/download
    fileSize: number;
    mimeType: string;
    type: DocumentationType;
    accountHolderId: string;
    relatedAuctionId?: string | null;
    isPolicyAttested?: boolean;
    policyAttestedAt?: Date | null;
  };
  r2Key: string;
}

/**
 * Unified service for handling policy file uploads to Cloudflare R2
 * Used by both new policy creation and auction policy upload flows
 */
export class PolicyUploadService {
  /**
   * Validates a policy document using AI extraction
   * NOTE: Currently disabled for MVP phase, but ready for future implementation
   * @param file - The file to validate
   * @returns Validation result
   */
  static async validatePolicyWithAI(file: File): Promise<{ isValid: boolean; reason?: string; errorCode?: string }> {
    try {
      const modelName = process.env.GEMINI_MODEL || "gemini-1.5-flash";
      const model = genAI.getGenerativeModel({ model: modelName });

      const fileBuffer = Buffer.from(await file.arrayBuffer());
      const filePart: Part = {
        inlineData: {
          data: fileBuffer.toString("base64"),
          mimeType: file.type,
        },
      };

      const validationPrompt = `
        You are a document validator for an insurance platform. Your task is to determine if the uploaded document is a valid insurance policy.

        A valid insurance policy should contain:
        - Policy number or reference
        - Insurance company name
        - Policy holder information
        - Coverage details or insured items
        - Policy dates (start/end dates)
        - Premium or payment information

        Analyze the document and determine if it's a legitimate insurance policy document.

        Respond with ONLY a JSON object in this exact format:
        {"isValid": true, "reason": "Document contains all required policy elements"}
        OR
        {"isValid": false, "reason": "Document is missing required policy information"}
      `;

      const validationResult = await model.generateContent({
        contents: [{ role: "user", parts: [{ text: validationPrompt }, filePart] }],
      });

      const validationText = validationResult.response.text();

      // Clean up the response text to extract JSON
      const jsonMatch = validationText.match(/\{[^}]*\}/);
      if (!jsonMatch) {
        throw new Error("Invalid response format from AI");
      }

      const validation = JSON.parse(jsonMatch[0]);

      if (!validation.isValid) {
        return {
          isValid: false,
          reason: validation.reason,
          errorCode: "UNSUPPORTED_DOCUMENT_TYPE"
        };
      }

      return { isValid: true };
    } catch (error) {
      console.error("AI validation error:", error);
      return {
        isValid: false,
        reason: "Error during document validation",
        errorCode: "GENERIC_VALIDATION_FAILURE"
      };
    }
  }

  /**
   * Uploads a policy file to R2 and creates a documentation record
   * @param options - Upload configuration options
   * @returns Upload result with documentation record and URLs
   */
  static async uploadPolicyFile(options: PolicyUploadOptions): Promise<PolicyUploadResult> {
    const {
      file,
      fileName,
      userId,
      accountHolderId,
      documentType,
      relatedAuctionId,
      isPolicyAttested = false,
      location = "policies",
      skipAIValidation = true // Default to true for MVP phase
    } = options;

    // Validate the file using centralized utility
    validatePolicyDocument(file);

    // AI validation for policy documents (currently disabled for MVP phase)
    // To re-enable: set skipAIValidation = false in method calls
    if (!skipAIValidation) {
      const aiValidation = await this.validatePolicyWithAI(file);
      if (!aiValidation.isValid) {
        throw new Error(`Document validation failed: ${aiValidation.reason}`);
      }
    }

    // Sanitize filename
    const sanitizedFilename = fileName ? sanitizeFilename(fileName) : sanitizeFilename(file.name);

    // Upload file to Cloudflare R2
    // Structure: policies/userId/filename
    const r2Key = await uploadToR2(file, `${location}/${userId}`);

    // Create documentation record
    // Store R2 key directly - documents are accessed via secure API route
    const documentation = await db.documentation.create({
      data: {
        fileName: sanitizedFilename,
        url: r2Key, // Store R2 key for secure access via /api/documents/download
        fileSize: file.size,
        mimeType: file.type,
        type: documentType,
        accountHolderId,
        relatedAuctionId,
        isPolicyAttested,
        policyAttestedAt: isPolicyAttested ? new Date() : null,
      },
    });

    return {
      documentation,
      r2Key,
    };
  }

  /**
   * Uploads a new policy document for auction completion
   * @param file - The policy file to upload
   * @param fileName - Optional custom filename
   * @param userId - User ID for folder organization
   * @param accountHolderId - Account holder ID
   * @param auctionId - Related auction ID
   * @returns Upload result
   */
  static async uploadAuctionPolicyFile(
    file: File,
    fileName: string | undefined,
    userId: string,
    accountHolderId: string,
    auctionId: string
  ): Promise<PolicyUploadResult> {
    return this.uploadPolicyFile({
      file,
      fileName,
      userId,
      accountHolderId,
      documentType: "NEW_POLICY_DOCUMENT",
      relatedAuctionId: auctionId,
      isPolicyAttested: true,
      location: "policies",
      skipAIValidation: true // Skip AI validation for MVP phase
    });
  }

  /**
   * Uploads a policy document for new policy creation
   * @param file - The policy file to upload
   * @param fileName - Optional custom filename
   * @param userId - User ID for folder organization
   * @param accountHolderId - Account holder ID
   * @returns Upload result
   */
  static async uploadNewPolicyFile(
    file: File,
    fileName: string | undefined,
    userId: string,
    accountHolderId: string
  ): Promise<PolicyUploadResult> {
    return this.uploadPolicyFile({
      file,
      fileName,
      userId,
      accountHolderId,
      documentType: "POLICY_DOCUMENT",
      isPolicyAttested: false,
      location: "policies",
      skipAIValidation: true // Skip AI validation for MVP phase
    });
  }
}
