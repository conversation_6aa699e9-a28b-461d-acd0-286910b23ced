import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/api-auth";
import { resolveAccountHolderProfile } from "@/features/account-holder/utils/profile-resolver";
import { createSuccessResponse, handleApiError, ApiResponses } from "@/lib/api-responses";
import { PolicyUploadService } from "@/lib/services/policy-upload.service";
import { z } from "zod";
import { AssetType } from "@prisma/client";

const createPolicySchema = z.object({
  type: z.enum(["CAR", "MOTORCYCLE"]),
  file: z.any().optional(), // Use z.any() for File objects in server environment
  termsAccepted: z.boolean().refine(val => val === true, {
    message: "Terms and conditions must be accepted"
  }),
});



export async function POST(request: NextRequest) {
  try {
    // Authenticate user using centralized utility
    const user = await getCurrentUser(request);

    // Parse the form data
    const formData = await request.formData();
    const type = formData.get("type") as AssetType;
    const file = formData.get("file") as File | null;
    const termsAccepted = formData.get("termsAccepted") === "true";

    const validationResult = createPolicySchema.safeParse({
      type,
      file: file || undefined,
      termsAccepted,
    });

    if (!validationResult.success) {
      return ApiResponses.badRequest(
        "Datos inválidos",
        "VALIDATION_ERROR",
        validationResult.error.errors
      );
    }

    let documentPath = null;
    let policyId = null;

    if (file) {
      // Resolve account holder profile using centralized utility
      const accountHolderProfile = await resolveAccountHolderProfile(user);

      // Upload file using unified service
      const uploadResult = await PolicyUploadService.uploadNewPolicyFile(
        file,
        file.name,
        user.id, // Use user ID for folder organization
        accountHolderProfile.id
      );

      // Create the Asset record first
      const asset = await db.asset.create({
        data: {
          accountHolderId: accountHolderProfile.id,
          assetType: type,
          description: `${type === "CAR" ? "Coche" : "Motocicleta"} - Pendiente de completar información`,
        },
      });

      // Create the Vehicle record linked to the Asset
      await db.vehicle.create({
        data: {
          assetId: asset.id,
          // Initial creation with minimal data - will be populated by AI extraction
        },
      });

      // Then create the Policy record linking to both Documentation and Asset
      const newPolicy = await db.policy.create({
        data: {
          accountHolder: {
            connect: {
              id: accountHolderProfile.id,
            },
          },
          document: {
            connect: {
              id: uploadResult.documentation.id,
            },
          },
          asset: {
            connect: {
              id: asset.id,
            },
          },
          isAssetsTypeConfirmed: true,
          status: "DRAFT",
          termsAccepted: termsAccepted,
          termsAcceptedAt: termsAccepted ? new Date() : null,
        },
      });
      documentPath = uploadResult.r2Key;
      policyId = newPolicy.id;
    }

    // AI extraction and policy population is now handled by PolicyUploadService
    // No additional processing needed here for MVP phase

    return createSuccessResponse({
      message: "Póliza registrada con éxito",
      documentPath,
      policyId,
      assetType: type,
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// AI extraction and policy population functionality has been moved to PolicyUploadService
// This consolidation eliminates code duplication and follows DRY principles
