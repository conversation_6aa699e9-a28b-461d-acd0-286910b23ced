import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { AuctionState, PolicyStatus } from "@prisma/client";
import { getCurrentUser } from "@/lib/api-auth";
import { createSuccessResponse, handleApiError, ApiResponses } from "@/lib/api-responses";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user using centralized utility
    const user = await getCurrentUser(request);

    // Await params to access its properties
    const { id } = await params;

    // Verify auction exists and belongs to the user
    const auction = await db.auction.findFirst({
      where: {
        id: id,
        accountHolder: {
          userId: user.id,
        },
        status: AuctionState.SIGNED_POLICY, // Should already be SIGNED_POLICY after policy upload
      },
      include: {
        policy: {
          include: {
            document: true,
          },
        },
        newPolicyDocument: true,
      },
    });

    if (!auction) {
      return ApiResponses.notFound(
        "Subasta no encontrada o no autorizada",
        "AUCTION_NOT_FOUND"
      );
    }

    // Verify that a new policy document has been uploaded
    if (!auction.newPolicyDocumentId) {
      return ApiResponses.badRequest(
        "Debe subir la nueva póliza antes de completar",
        "NEW_POLICY_DOCUMENT_REQUIRED"
      );
    }

    // Auction is already finalized, just return the current state
    const updatedAuction = await db.auction.findFirst({
      where: {
        id: id,
      },
      include: {
        policy: {
          include: {
            document: true,
          },
        },
        newPolicyDocument: true,
        selectedBid: {
          include: {
            broker: {
              include: {
                user: true,
              },
            },
          },
        },
        bids: {
          include: {
            broker: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    // Update policy status to ACTIVE
    await db.policy.update({
      where: {
        id: auction.policyId,
      },
      data: {
        status: PolicyStatus.ACTIVE,
      },
    });

    return createSuccessResponse(
      { auction: updatedAuction },
      "Subasta completada exitosamente"
    );
  } catch (error) {
    return handleApiError(error);
  }
}