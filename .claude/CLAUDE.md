# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build application for production (uses --no-lint flag)
- `npm run start` - Start production server
- `npm run lint` - Lint codebase with Next.js ESLint

### Database Operations
- `npm run db:rebuild` - **Complete database rebuild** - Drops all data, applies migrations, sets up infrastructure (extensions, functions, cron jobs), and seeds test data
- `npm run migrate:dev` - Run Prisma migrations for development
- `npm run db:seed` - Seed database with comprehensive test data
- `npm run db:studio` - Open Prisma Studio for database management

### Database Infrastructure
- `npm run db:setup-infrastructure` - Set up Supabase extensions, functions, and cron jobs
- `npm run db:apply-policies` - Apply Row-Level Security policies

### Test Users (created by seeding)
- Account Holder: `<EMAIL>` / `Abcdef7*`
- Broker: `<EMAIL>` / `Abcdef7*` 
- Admin: `<EMAIL>` / `Abcdef7*`

## Architecture

### Screaming Architecture (100% Compliant)
This codebase strictly follows **Screaming Architecture** principles - the structure reflects business domains, not technical layers.

**Core Structure:**
```
src/
├── app/                    # Next.js App Router - role-based organization
│   ├── (public)/          # Public routes (login, signup)
│   ├── account-holder/    # Account holder dashboard and features
│   ├── broker/            # Broker dashboard and features
│   ├── admin/             # Admin dashboard and features
│   └── api/               # Server-side API routes
├── features/              # Business domain features (primary organization)
│   ├── account-holder/    # Account holder specific logic & components
│   ├── broker/            # Broker specific logic & components
│   ├── admin/             # Admin specific features
│   ├── auctions/          # Auction business logic
│   ├── policies/          # Policy business logic
│   ├── auth/              # Authentication logic
│   └── support/           # Support functionality
├── components/
│   ├── ui/                # Generic shadcn/ui components only
│   └── shared/            # App-wide, non-domain components
└── lib/                   # Pure infrastructure (database, utilities, configs)
```

### Key Principles
- **Domain-Driven Structure**: Organize by business domain (user roles: account-holder, broker, admin)
- **DRY Compliance**: Single source of truth for all logic
- **Role-Based Access**: All routes and features organized by user roles
- **Server-Side Security**: All database operations through authenticated API routes

## Technology Stack

### Core Framework
- **Next.js 15+** with App Router and Turbopack
- **TypeScript** (100% coverage, strict mode)
- **Prisma ORM** with PostgreSQL
- **Supabase** for auth, database hosting, and Row-Level Security

### UI/Styling
- **Tailwind CSS** with custom design system
- **Radix UI** components via shadcn/ui
- **React Hook Form** with Zod validation
- **Lucide React** icons

### External Services
- **Google Gemini API** for AI-powered policy document extraction
- **Cloudflare R2** for document storage
- **Brevo** for email notifications

## Database Schema

### Core Models
- **User**: Base user with role (`ACCOUNT_HOLDER`, `BROKER`, `ADMIN`)
- **Policy**: Insurance policy with complete lifecycle (DRAFT → ACTIVE → RENEW_SOON → EXPIRED)
- **Auction**: Reverse auction system for insurance policies with working hours logic
- **Documentation**: File management linked to policies and auctions

### Important Business Logic

#### Auction Working Hours
- **Working Days**: Monday-Friday only
- **Working Hours**: 06:00-23:59 Madrid timezone (18h/day)
- **Duration**: 48 working hours (~2.67 business days)
- **Weekend handling**: Automatically moves start times to Monday 06:00
- **Implementation**: `src/features/auctions/utils/business-hours.ts`

#### Policy Lifecycle Management
- Complete workflow from DRAFT → ACTIVE → RENEW_SOON → EXPIRED
- AI-powered document extraction using Google Gemini API
- Integrated with auction system for policy renewal

## Development Guidelines

### File Organization (CRITICAL)
- **Domain Features**: Place all business logic in appropriate `src/features/` directory
- **Role-Specific Routes**: Place in `src/app/{role}/` (account-holder, broker, admin)
- **Shared Components**: Use `src/components/shared/` for app-wide components
- **UI Components**: Only generic shadcn/ui components in `src/components/ui/`
- **Infrastructure**: Database, auth, and utility code in `src/lib/`

### Security Requirements (NON-NEGOTIABLE)
- **Server-Side Operations**: All database operations must go through API routes
- **No Client-Side DB Access**: NEVER use `@/lib/supabase/client` for database operations
- **Mandatory Pattern**: Client → API Route → Server-side Supabase → Database
- **Row-Level Security**: RLS policies enforce data access controls
- **Authentication**: Supabase Auth with JWT validation server-side

### Code Quality Standards
- **TypeScript**: Strict mode enabled, zero build errors tolerance
- **Language Conventions**: 
  - Code (variables, functions, files): English
  - UI text (labels, buttons, messages): Professional Spanish
- **Linting**: ESLint with Next.js configuration
- **Schema Generation**: Auto-generated Zod schemas from Prisma (`src/lib/zod/`)

### Component Standards
- **Single Responsibility**: Each component has one clear purpose
- **Proper Typing**: All props and functions properly typed with TypeScript
- **Accessibility**: Consider accessibility in all UI components
- **Error Handling**: Consistent error handling patterns throughout

## Service Architecture

### PolicyUploadService
Centralized service for all file upload operations:
- **Location**: `src/lib/services/policy-upload.service.ts`
- **Features**: AI validation, Cloudflare R2 uploads, documentation records
- **Usage**: Both new policy creation and auction policy uploads

### Key Services
- **Authentication**: `src/features/auth/` - Supabase Auth integration
- **Document Management**: Cloudflare R2 with secure server-side operations
- **AI Integration**: Google Gemini API for document analysis

## API Architecture

### Server-Side Security (MANDATORY)
```
Client → Next.js API Route → Server-side Supabase → Database
```

### API Structure
```
src/app/api/
├── account-holder/        # Account holder operations
├── broker/               # Broker operations  
├── admin/               # Admin operations
├── policies/            # Policy management
├── auctions/            # Auction operations
└── documents/           # Document management
```

### Security Checklist for API Routes
- ✅ Server-side authentication validation
- ✅ Zod schema validation for inputs
- ✅ Proper error handling without exposing internals
- ✅ File operations handled server-side only

## Important Notes

### Known Issues
- Database seeding with `SIGNED_POLICY` status may cause issues (see changelog)
- Edge Function deployment requires Docker for local development

### Recent Updates (September 2025)
- **PolicyUploadService**: Unified file upload logic with AI validation
- **DRY Implementation**: Eliminated code duplication across upload flows
- **Enhanced Components**: Flexible PolicyFileUploadStep for multiple use cases

### Configuration
- Environment variables defined in `.env.example`
- Prisma generates client to `node_modules/.prisma/client`
- Zod schemas auto-generated to `src/lib/zod/`

### Color Palette (Brand Standards)
- White (`#FFFFFF`)
- Black (`#000000`) 
- Aquamarine Green (`#6BE1A6`)

## Critical Anti-Patterns (DO NOT DO)
- ❌ Never create technical domains (`src/app/auctions/`, `src/app/policies/`)
- ❌ Never use client-side Supabase for database operations
- ❌ Never create multiple Prisma clients (use singleton from `src/lib/db.ts`)
- ❌ Never duplicate logic across components/services
- ❌ Never place domain-specific logic in generic locations
- ❌ Never expose sensitive data in logs or error messages

## Winner Bid Selection System

### Automatic Winner Selection
The platform implements a sophisticated automated winner selection system for auction closure:

#### Winner Selection Algorithm
- **Location**: `src/lib/auction/winner-selection.service.ts`
- **Criteria**: 70% price weight, 30% coverage quality weight
- **Winners**: Top 3 bidders selected automatically
- **Processing**: Fully automated when auctions close

#### Manual Override (Account Holder)
Account holders can override automatic selection through:
- **Component**: `SelectBestOfferModal` - Manual winner selection interface
- **Location**: `src/features/account-holder/components/SelectBestOfferModal.tsx`
- **Action**: `src/features/account-holder/auctions/actions/select-winners.ts`
- **UI**: Winner selection form with bid comparison and confirmation

#### Winner Management Workflow
1. **Automatic Selection**: Edge Function selects top 3 winners on auction closure
2. **Database Storage**: Winners stored in `AuctionWinner` table with position ranking
3. **Failsafe System**: Database trigger creates winners if Edge Function fails
4. **Contact Revelation**: Automatic contact information sharing between winners and account holders
5. **Notifications**: Email notifications to all parties (winners, account holders, admin)

#### Winner Display System
- **Closed Auctions**: Beautiful card layout with medal emojis (🥇🥈🥉)
- **Position Tracking**: Database-stored position rankings (1st, 2nd, 3rd place)
- **Contact Information**: Revealed automatically upon winner selection
- **Commission System**: 10% of policy premium paid by winning brokers

## Working Hours Business Logic

### Auction Duration Calculation
All auctions follow sophisticated working hours logic:

#### Examples
- **Monday 10:00 start** → Closes Wednesday 22:00 (14h + 18h + 16h = 48h)
- **Friday 18:00 start** → Closes Wednesday 12:00 (6h + skip weekend + 18h + 18h + 6h = 48h)
- **Saturday start** → Moved to Monday 06:00, closes Wednesday 18:00

#### Implementation Details
- **Core Logic**: `src/features/auctions/utils/business-hours.ts`
- **Automation**: Supabase cron jobs close expired auctions every 5 minutes
- **Database Field**: Single `endDate` field with consolidated approach

This platform represents a gold standard implementation of Screaming Architecture with 100% business domain visibility and comprehensive security patterns.