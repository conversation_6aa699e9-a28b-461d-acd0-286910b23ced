# Zeeguros - Reverse Auction Platform for Insurance

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
[![Version](https://img.shields.io/badge/version-0.1.0-blue)](./package.json)
[![Architecture](https://img.shields.io/badge/architecture-screaming-green)](./docs/architecture.md)
[![TypeScript](https://img.shields.io/badge/typescript-100%25-blue)](./tsconfig.json)

Zeeguros is a reverse auction platform for insurance policies, where users upload their current policy and insurance brokers compete to offer better coverage or price.

## Description

Zeeguros is a comprehensive web application designed to modernize the insurance industry. It provides a robust set of tools for users to manage their car insurance policies, for brokers to manage their client portfolios, and for the platform to leverage cutting-edge AI for data automation. The core of the platform is built on a modern technology stack, featuring Next.js for the frontend, Supabase for authentication and database management, and Google's Gemini AI for intelligent data extraction from policy documents.

## 🚀 Key Features

*   **🤖 AI-Powered Onboarding**: Intelligent policy data extraction using Google Gemini API
*   **📋 Data Validation**: Comprehensive validation of extracted policy information
*   **📄 Policy Management**: Complete policy lifecycle management with status tracking
*   **📁 Document Management**: Secure file upload, storage, and download using Cloudflare R2
*   **🏛️ Insurance Auctions**: Reverse auction system where brokers compete for policies
*   **🔐 Secure Authentication**: Role-based access control with Supabase Auth and comprehensive RLS policies
*   **📊 User Dashboards**: Tailored interfaces for account holders, brokers, and administrators
*   **📱 Mobile Responsive**: Optimized mobile experience with consistent UI/UX across all pages
*   **⚡ Scalable Backend**: Built on Next.js 15+ with TypeScript and Prisma ORM
*   **☁️ Cloud Storage**: Cloudflare R2 for cost-effective, globally distributed file storage

## Architecture Overview

Zeeguros is built as a **Role-Based Monolith** with **Next.js 15+**, featuring clean separation between user roles (`ACCOUNT_HOLDER`, `BROKER`, `ADMIN`) and organized around business domains rather than technical layers. The application enforces **server-side security** where all database operations are handled through authenticated API Routes, ensuring data protection and proper access control.

**Core Architecture:**
- **Frontend**: Next.js 15+ with TypeScript and role-based routing
- **Backend**: Next.js API Routes with centralized authentication
- **Database**: PostgreSQL via Supabase with Prisma ORM
- **Security**: Server-side only database access with comprehensive RLS policies

## Business Logic

### Auction Duration Calculation

Auctions in Zeeguros follow a sophisticated working hours business logic to ensure fair and consistent timing across all insurance policy auctions:

#### Working Hours Configuration
- **Working Days**: Monday through Friday only
- **Working Hours**: 06:00 to 23:59 (Madrid timezone)
- **Daily Working Hours**: 18 hours per day (06:00:00 to 23:59:59)
- **Total Auction Duration**: 48 working hours (~2.67 business days)

#### Calculation Rules
1. **Weekend Exclusion**: Saturday and Sunday hours are completely excluded from duration calculations
2. **Pre-Working Hours**: Auctions starting before 06:00 are automatically moved to 06:00 of the same day
3. **Weekend Start**: Auctions starting on weekends are moved to Monday 06:00
4. **Timezone Handling**: All calculations use Europe/Madrid timezone for consistency
5. **End Date Calculation**: The `endDate` field is calculated using the working hours logic and represents when auctions actually close

#### Implementation
- **Core Logic**: Located in `src/lib/auction/working-hours.ts`
- **Key Function**: `calculateWorkingHoursClosedAt(startDate, 48)`
- **Database Field**: Single `endDate` field (consolidated from previous dual-field approach)
- **Automation**: Supabase cron jobs automatically close expired auctions every 5 minutes

#### Examples
- **Monday 10:00 start** → Closes Wednesday 22:00 (14h + 18h + 16h = 48h)
- **Friday 18:00 start** → Closes Wednesday 12:00 (6h + skip weekend + 18h + 18h + 6h = 48h)
- **Saturday start** → Moved to Monday 06:00, closes Wednesday 18:00 (18h + 18h + 12h = 48h)

### 📚 **Comprehensive Documentation**

This project maintains extensive documentation to support development and architectural decisions:

- **[Main Architecture Document](docs/architecture.md)**: Complete architectural blueprint and technical specifications
- **[Architecture Details](docs/architecture/)**: Detailed architectural documentation including:
  - Enhancement scope and tech stack alignment
  - Data models and component architecture
  - API design and external integrations
  - Infrastructure, security, and testing strategies
- **[Development Plans](docs/plans/)**: Current development roadmaps and PRDs
  - [Account Holder Journey Enhancement PRD](docs/plans/account-holder-journey-enhancement-prd.md): Active development plan for policy management completion
- **[Change History](docs/changelog/)**: Comprehensive version history and architectural evolution
- **[User Stories](docs/stories/)**: BMAD-driven development stories and requirements

### Architectural Principles

Zeeguros follows **Domain-Driven Design** principles, organizing code by business domains rather than technical layers. The codebase structure directly reflects our three-sided marketplace with clear separation between:

- **Account Holders**: Policy owners who create auctions
- **Brokers**: Insurance professionals who bid on auctions  
- **Admins**: Platform administrators who oversee operations

This approach ensures high maintainability, clear domain boundaries, and scalable feature development while following **DRY principles** to eliminate code duplication.
### Database Architecture

Zeeguros uses a PostgreSQL database with a well-structured schema supporting the complete insurance marketplace workflow. The database includes comprehensive models for:

- **User Management**: Role-based profiles for account holders, brokers, and admins
- **Policy Lifecycle**: Complete insurance policy management from creation to expiration
- **Auction System**: Reverse auction functionality with bid management and winner selection
- **Document Management**: Secure file storage integration with detailed metadata
- **Address & Asset Management**: Complete vehicle and location data for accurate quotes

For the detailed Entity-Relationship Diagram and complete schema specifications, see the [Architecture Document](docs/architecture.md).


### Document Management

Zeeguros includes a comprehensive document management system for policy documents:

- **Secure Upload**: Policy documents stored in Cloudflare R2 with server-side processing
- **AI Integration**: Google Gemini API extracts policy data automatically
- **Access Control**: Role-based document access with authentication required
- **File Support**: PDF, JPG, and PNG files with validation and metadata tracking

## Recent Updates

### September 2025 - Service Layer Consolidation
- **PolicyUploadService**: Unified file upload logic with AI validation across all flows
- **API Standardization**: Centralized authentication and response formatting
- **Repository Cleanup**: Removed legacy development tools (69+ files)
- **Enhanced Components**: More flexible and reusable UI components

### August 2025 - Architecture & Security Improvements  
- **100% Screaming Architecture**: Complete role-based organization achieved
- **Security Hardening**: Fixed database triggers and implemented comprehensive RLS policies
- **Mobile Optimization**: Enhanced responsive design across all interfaces
- **Document Management**: Complete Cloudflare R2 integration with secure file handling

For detailed change history, see the [changelog directory](docs/changelog/).

### 🏛️ System Architecture Diagram

This diagram provides a detailed view of the **Role-Based Monolith** architecture, illustrating the flow of requests and the separation of concerns between the client, server, and external services.

```mermaid

graph TD
    subgraph Browser["User's Browser"]
        A[Next.js Client Components]
        B[Role-Based Routes]
    end

    subgraph Server["Zeeguros Server - OCI ARM Ampere VPS"]
        C[Next.js API Routes]
        D[Prisma Client]
        E[Google Gemini API]
        F[Brevo API]
    end

    subgraph Infrastructure["Infrastructure - Supabase"]
        G[PostgreSQL Database]
        H[Supabase Auth]
        I[JWT Validation]
    end

    A -->|User Interaction| B
    B -->|API Request| C
    C -->|DB Operations| D
    C -->|AI Processing| E
    C -->|Email Notifications| F
    D -->|SQL Query| G
    
    H -->|Validates JWT| I
    I -->|Auth Check| C
    
    G -.->|RLS Policies| D

    classDef clientStyle fill:#cce5ff,stroke:#333,stroke-width:2px
    classDef serverStyle fill:#d4f0f0,stroke:#333,stroke-width:2px
    classDef infraStyle fill:#FDEDEC,stroke:#333,stroke-width:2px
    classDef authStyle fill:#e6e6fa,stroke:#333,stroke-width:2px
    
    class A,B clientStyle
    class C,D,E,F serverStyle
    class G infraStyle
    class H,I authStyle
```

### AI-Powered Policy Processing

Zeeguros uses Google Gemini API to automatically extract policy data from uploaded documents:

1. **Document Upload**: Users upload policy documents (PDF, JPG, PNG)
2. **AI Processing**: Google Gemini extracts structured data from documents
3. **Validation**: Multi-layer validation ensures data accuracy and security
4. **Form Population**: Extracted data populates policy creation forms automatically

This reduces manual data entry by ~90% and improves accuracy while maintaining security through server-side processing.



### 🛠️ Technology Stack

#### **Frontend**
*   **Next.js 15+**: React framework with App Router
*   **TypeScript**: Type-safe development (100% coverage)
*   **Tailwind CSS**: Utility-first CSS framework
*   **Radix UI**: Accessible component primitives
*   **React Hook Form**: Form management with validation
*   **Zod**: Schema validation
*   **Lucide React**: Modern icon library

#### **Backend**
*   **Next.js API Routes**: Server-side API endpoints
*   **Prisma ORM**: Database toolkit and query builder
*   **PostgreSQL**: Primary database (via Supabase)
*   **Supabase**: Authentication, database hosting, and Row-Level Security
*   **AWS SDK v3**: S3-compatible operations for Cloudflare R2

#### **AI & External Services**
*   **Google Gemini API**: AI-powered document analysis
*   **Brevo**: Email service provider for notifications
*   **Cloudflare R2**: Object storage for documents

#### **Development Tools**
*   **ESLint**: Code linting with custom rules
*   **Prettier**: Code formatting
*   **TypeScript**: Static type checking
*   **Prisma Studio**: Database management interface

## Database Schema

The database schema is defined and managed using Prisma. Here are the core models:

*   **`User`**: Stores user authentication information and role (`ACCOUNT_HOLDER`, `BROKER`, or `ADMIN`).
*   **`AccountHolderProfile` / `BrokerProfile` / `AdminProfile`**: Store role-specific data, linked to a `User`.
*   **`Policy`**: Contains all details of an insurance policy, linking together the customer, broker, asset, and coverages.
*   **`Asset`**: Stores detailed information about the insured asset.
*   **`InsuredParty`**: Represents individuals covered by a policy (e.g., policyholder, owner, driver) and links to a generic `Person` model.
*   **`Coverage`**: Defines the specific guarantees and financial details (limits, deductibles) of a policy.
*   **`Auction`**: Manages the insurance auction data, linked to a user and a asset.
*   **`AuctionBid`**: Stores bids made by brokers on auctions.

For the complete and detailed schema, please see [`prisma/schema.prisma`](prisma/schema.prisma).

## Prerequisites

Before you begin, ensure you have the following installed:

*   **Node.js** (v18.17.0 or higher)
*   **npm** or your preferred package manager
*   **Git**
*   A [Supabase](https://supabase.com/) project for database and authentication
*   A [Google Gemini API Key](https://aistudio.google.com/app/apikey)
*   **Cloudflare R2** account for document storage

## Installation and Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/zee-next-app.git
    cd zee-next-app
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file by copying the example file:
    ```bash
    cp .env.example .env.local
    ```
    Then, fill in the required values in `.env.local`:
    *   `NEXT_PUBLIC_SITE_URL`: The public URL of your application (e.g., `http://localhost:3000`)
    *   `DATABASE_URL`: Your Supabase database connection string (with pooling)
    *   `DIRECT_URL`: Your Supabase direct database connection string (for migrations)
    *   `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
    *   `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
    *   `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
    *   `SUPABASE_ACCESS_TOKEN`: Your Supabase access token
    *   `GEMINI_API_KEY`: Your Google Gemini API key
    *   `GEMINI_MODEL`: The Gemini model to use (e.g., `gemini-2.5-flash-lite-preview-06-17`)
    *   `BREVO_API_KEY`: Your Brevo API key for email notifications
    *   `R2_ACCOUNT_ID`: Your Cloudflare R2 Account ID
    *   `R2_ACCESS_KEY_ID`: Your Cloudflare R2 Access Key ID
    *   `R2_SECRET_ACCESS_KEY`: Your Cloudflare R2 Secret Access Key
    *   `R2_BUCKET_NAME`: Your Cloudflare R2 bucket name

4.  **Run database migrations:**
    ```bash
    npx prisma migrate dev
    ```

5.  **Seed the database:**
    ```bash
    npm run db:seed
    ```
    
    **Note**: If you encounter issues with the `SIGNED_POLICY` status during seeding, this is a known issue documented in the [August 11, 2025 changelog](docs/changelog/2025-08/2025-08-11-changelog-42.md).

## Getting Started

To get a local copy up and running, follow these simple steps.

### Database Setup

If you are starting from a clean database, or if you need to reset your local environment, run the following commands in order:

1.  **Remove existing migrations:**
    ```bash
    rm -rf prisma/migrations
    ```
2.  **Reset the database:** This command will drop the database, apply all migrations, and ensure your schema is up to date.
    ```bash
    npm run migrate:reset
    ```

3.  **Create the first migration:**
    ```bash
    npm run migrate:dev -- --name "first_schema"
    ```

4.  **Seed the database:** This will populate the database with essential test data, including user accounts for each role.
    ```bash
    npm run db:seed
    ```

### Complete Database Rebuild

For a **complete database rebuild from scratch** (recommended for development and testing), use the master rebuild command:

```bash
npm run db:rebuild
```

This single command performs the following operations in sequence:

1. **`npm run migrate:reset -- --force`**: Drops all data and resets Prisma migrations
2. **`npm run db:setup-infrastructure`**: Sets up Supabase infrastructure:
   - **Extensions**: Enables `pg_cron` and `pg_net` for automation
   - **Functions**: Creates notification logging, statistics, and auction management functions
   - **Cron Jobs**: Schedules auction expiration and notification jobs (every 5 minutes)
3. **`npm run db:setup-migrations`**: Applies additional SQL migrations:
   - **Auction Expiration Logic**: Timezone-aware auction closure with working hours support
   - **Manual Functions**: Testing and emergency auction closure functions
4. **`npm run db:setup-config`**: Configures database settings (may require manual setup)
5. **`npm run db:deploy-functions`**: Deploys Supabase Edge Functions (requires Docker)
6. **`npm run db:apply-policies`**: Applies Row-Level Security (RLS) policies
7. **`npm run db:seed`**: Populates database with comprehensive test data

**Use this command when:**
- Setting up a new development environment
- Resetting your local database completely
- Testing database schema changes
- Preparing for deployment testing

**Note**: The Edge Function deployment step requires Docker to be running. If Docker is not available, the other steps will still complete successfully.

### Zod Schema Generation

If you encounter issues with generated Zod schemas, you can regenerate them with the following commands:

```bash
rm -rf src/lib/zod
npx prisma generate
```

### Test User Credentials

The seed script creates the following test users:

*   **Account Holder:** `<EMAIL>` / `Abcdef7*`
*   **Broker:** `<EMAIL>` / `Abcdef7*`
*   **Admin:** `<EMAIL>` / `Abcdef7*`

## Running the Project

To run the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

### Available Scripts

*   `npm run dev`: Starts the development server with Turbopack
*   `npm run build`: Builds the application for production
*   `npm run start`: Starts a production server
*   `npm run lint`: Lints the codebase with custom ESLint rules
*   `npm run migrate:dev`: Runs database migrations for development
*   `npm run db:seed`: Seeds the database with comprehensive test data
*   `npm run db:studio`: Opens Prisma Studio to view and manage your data
*   `npm run db:rebuild`: **Complete database rebuild from scratch** - Resets database, applies Prisma migrations, sets up infrastructure (extensions, functions, cron jobs), deploys Edge Functions, applies RLS policies, and seeds comprehensive test data
*   `npm run db:migrate`: Runs database migrations

## API Overview

Zeeguros provides a secure, role-based API architecture:

- **Server-Side Security**: All database operations through authenticated API routes
- **Role-Based Access**: Endpoints organized by user roles (account-holder, broker, admin)
- **Unified Service Layer**: PolicyUploadService handles file operations with AI validation
- **Standardized Responses**: Consistent response formatting across all endpoints

**Key Endpoints:**
- Policy creation with AI extraction
- Secure document upload/download  
- Role-based data access
- Auction management and bidding

For detailed API specifications, see the [Architecture Document](docs/architecture.md).

## Contributing

Contributions are welcome! Please follow the standard GitHub flow:

1.  Fork the repository.
2.  Create a new branch (`git checkout -b feature/your-feature`).
3.  Make your changes and commit them (`git commit -m 'Add some feature'`).
4.  Push to the branch (`git push origin feature/your-feature`).
5.  Open a Pull Request.

### Development Guidelines

*   **Follow Screaming Architecture:** All new features must be organized by business domain in `src/features/`
*   **Respect Component Boundaries:** Use `src/components/ui/` for generic components only, `src/components/shared/` for app-wide components
*   **Maintain Code Quality:** All code must pass TypeScript compilation without `ignoreBuildErrors`
*   **DRY Principle:** Avoid duplicate logic, use existing services and utilities
*   **Documentation:** Update relevant documentation in `docs/` for architectural changes

#### **📖 Development Resources**

- **[Main Architecture Document](docs/architecture.md)**: Complete architectural standards and patterns
- **[Current Development Plan](docs/plans/account-holder-journey-enhancement-prd.md)**: Active PRD for account holder journey completion
- **[Latest Changes](docs/changelog/2025-07/)**: Recent architectural improvements and component relocations
- **[Architecture Details](docs/architecture/)**: Comprehensive technical specifications and integration guidelines

#### **🎯 Current Development Focus**

The platform is currently focused on completing the **Account Holder Journey Enhancement** as outlined in the [active PRD](docs/plans/account-holder-policies-prd.md), which includes:

- **✅ R2 Storage Migration**: Complete transition from Supabase storage to Cloudflare R2
- **✅ Document Management**: Secure file upload, storage, and download functionality implemented
- **✅ Server-Side Security**: All database operations through authenticated API routes with comprehensive RLS policies
- **✅ UI/UX Consistency**: Unified layout and mobile responsiveness across all account-holder pages
- **✅ Data Accuracy**: Fixed auction counting and filtering functionality
- **✅ Database Security**: Fixed user profile sync triggers and implemented comprehensive RLS policies
- **⏳ Policy Lifecycle Management**: Complete DRAFT → ACTIVE → RENEW_SOON → EXPIRED workflow
- **⏳ Brevo SMTP Integration**: Email notifications for policy status changes
- **⏳ Enhanced User Experience**: Advanced policy management and broker interaction features

#### **🐛 Known Issues**

- **Database Seeding**: `SIGNED_POLICY` status may cause issues during seeding (documented in [changelog](docs/changelog/2025-08/2025-08-11-changelog-42.md))
- **Policy-Auction Linking**: Integration between `RENEW_SOON` policies and auction system is in development

## 📈 Project Status & Roadmap

### **Current Status (August 2025)**

Zeeguros is in active development with a focus on completing the Account Holder Journey Enhancement. The platform has achieved significant milestones:

- **✅ Core Infrastructure**: Fully implemented with Next.js 15+, TypeScript, and Prisma
- **✅ Authentication & Security**: Complete role-based access control with comprehensive RLS policies
- **✅ Document Management**: Secure file handling with Cloudflare R2 integration
- **✅ AI Integration**: Google Gemini API for intelligent policy data extraction
- **✅ Mobile Responsiveness**: Optimized UI/UX across all device sizes
- **✅ Database Architecture**: Robust schema with proper relationships and constraints
- **✅ Policy Lifecycle**: Complete workflow from DRAFT to EXPIRED status

### **Upcoming Features**

- **📧 Email Notifications**: Brevo SMTP integration for policy status updates
- **🔗 Auction Integration**: Enhanced linking between RENEW_SOON policies and auctions
- **💰Stripe Payment Integration**: Secure payment processing for broker bidding
- **📊 Advanced Analytics**: Comprehensive reporting and insights dashboard
- **🌐 Multi-language Support**: Expanded localization beyond Spanish

### **Documentation**

For detailed technical requirements and implementation guidelines, see the comprehensive documentation:

- **[Architecture Overview](docs/architecture.md)**: Complete system architecture and design principles
- **[Product Requirements](docs/plans/account-holder-policies-prd.md)**: Current development focus and requirements
- **[Change History](docs/changelog/)**: Detailed changelog organized by month
- **[API Documentation](docs/api/)**: Complete API reference and examples

---

**Built with ❤️ by the Zeeguros team** | **Last updated: August 13, 2025**

## License

All rights reserved by ZEEGUROS PLATFORM S.L.
